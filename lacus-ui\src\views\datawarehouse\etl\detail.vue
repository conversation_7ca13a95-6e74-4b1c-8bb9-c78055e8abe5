<template>
  <el-dialog
    title="ETL任务详情"
    v-model="dialogVisible"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="detail-container">
      <!-- 操作按钮 -->
      <div class="detail-header">
        <el-button type="warning" @click="handleEdit" v-if="taskDetail.status !== 1">编辑</el-button>
        <el-button type="success" @click="handleRun" :loading="running">执行任务</el-button>
      </div>

      <!-- 基本信息 -->
      <el-card class="mb-4">
        <template #header>基本信息</template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务名称">{{ taskDetail.taskName }}</el-descriptions-item>
          <el-descriptions-item label="任务描述">{{ taskDetail.description }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="taskDetail.status === 1 ? 'success' : 'danger'">
              {{ taskDetail.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据流向">
            <el-tag type="info" size="small">{{ taskDetail.sourceLayer }}</el-tag>
            <el-icon style="margin: 0 5px;"><Right/></el-icon>
            <el-tag type="success" size="small">{{ taskDetail.targetLayer }}</el-tag>
          </el-descriptions-item>
        <el-descriptions-item label="调度方式" v-if="taskDetail && taskDetail.scheduleType !== undefined">
          <dict-tag :options="schedule_type" :value="taskDetail.scheduleType"/>
        </el-descriptions-item>
        <el-descriptions-item label="Cron表达式" v-if="taskDetail && taskDetail.scheduleType === 'CRON'">
          {{ taskDetail.cronExpression }}
        </el-descriptions-item>
          <el-descriptions-item label="目标表">{{ taskDetail.targetTable }}</el-descriptions-item>
          <el-descriptions-item label="写入模式">
            <el-tag>{{ getWriteModeText(taskDetail.writeMode) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(taskDetail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ parseTime(taskDetail.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后执行时间">{{ parseTime(taskDetail.lastRunTime) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 数据源配置 -->
      <el-card class="mb-4">
        <template #header>数据源配置</template>
        <el-table :data="taskDetail.sourceTables" style="width: 100%">
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="源表名" prop="tableName"/>
          <el-table-column label="表别名" prop="alias"/>
          <el-table-column label="过滤条件" prop="whereCondition" :show-overflow-tooltip="true"/>
        </el-table>
      </el-card>

      <!-- 字段映射配置 -->
      <el-card class="mb-4">
        <template #header>字段映射配置</template>
        <el-table :data="taskDetail.fieldMappings" style="width: 100%">
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="目标字段" prop="targetField"/>
          <el-table-column label="字段类型" prop="fieldType" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.fieldType }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="转换表达式" prop="expression" :show-overflow-tooltip="true"/>
          <el-table-column label="默认值" prop="defaultValue"/>
        </el-table>
      </el-card>

      <!-- 数据质量配置 -->
      <el-card class="mb-4" v-if="taskDetail.scheduleType === 'CRON'">
        <template #header>数据质量配置</template>
        <el-table :data="taskDetail.qualityRules" style="width: 100%" v-if="taskDetail.qualityRules && taskDetail.qualityRules.length > 0">
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="规则名称" prop="ruleName" width="150"/>
          <el-table-column label="检查字段" prop="checkField" width="150"/>
          <el-table-column label="规则类型" prop="ruleType" width="120">
            <template #default="scope">
              <el-tag size="small" :type="getQualityRuleTypeColor(scope.row.ruleType)">
                {{ getQualityRuleTypeText(scope.row.ruleType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="规则配置" prop="ruleConfig" :show-overflow-tooltip="true"/>
          <el-table-column label="严重级别" prop="severity" width="100">
            <template #default="scope">
              <el-tag size="small" :type="getSeverityTypeColor(scope.row.severity)">
                {{ getSeverityText(scope.row.severity) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="暂无数据质量配置" :image-size="80"/>
      </el-card>

      <!-- 执行历史 -->
      <el-card class="mb-4">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>执行历史</span>
            <el-button type="primary" size="small" @click="refreshExecutionHistory">刷新</el-button>
          </div>
        </template>
        <el-table :data="executionHistory" style="width: 100%" v-loading="historyLoading">
          <el-table-column label="执行ID" prop="executionId" width="120"/>
          <el-table-column label="执行状态" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" prop="startTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.startTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" prop="endTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.endTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="执行时长" prop="duration" width="100">
            <template #default="scope">
              <span>{{ formatDuration(scope.row.duration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="处理记录数" prop="processedRecords" width="120"/>
          <el-table-column label="错误信息" prop="errorMessage" :show-overflow-tooltip="true"/>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleViewLog(scope.row)">查看日志</el-button>
              <el-button type="info" size="small" @click="handleViewMetrics(scope.row)">查看指标</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 执行日志对话框 -->
    <el-dialog title="执行日志" v-model="logDialogVisible" width="80%" :before-close="handleCloseLogDialog">
      <div class="log-container">
        <pre>{{ executionLog }}</pre>
      </div>
    </el-dialog>

    <!-- 执行指标对话框 -->
    <el-dialog title="执行指标" v-model="metricsDialogVisible" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="输入记录数">{{ currentMetrics.inputRecords }}</el-descriptions-item>
        <el-descriptions-item label="输出记录数">{{ currentMetrics.outputRecords }}</el-descriptions-item>
        <el-descriptions-item label="错误记录数">{{ currentMetrics.errorRecords }}</el-descriptions-item>
        <el-descriptions-item label="跳过记录数">{{ currentMetrics.skippedRecords }}</el-descriptions-item>
        <el-descriptions-item label="平均处理速度">{{ currentMetrics.avgProcessingRate }} 条/秒</el-descriptions-item>
        <el-descriptions-item label="内存使用">{{ currentMetrics.memoryUsage }} MB</el-descriptions-item>
        <el-descriptions-item label="CPU使用率">{{ currentMetrics.cpuUsage }}%</el-descriptions-item>
        <el-descriptions-item label="网络IO">{{ currentMetrics.networkIO }} MB</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts" name="EtlTaskDetail">
import { ref, computed, watch, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'
import { getEtlTask, runEtlTask, getExecutionHistory as fetchExecutionHistory, getExecutionLog, getExecutionMetrics } from '@/api/datawarehouse/etl';

// 定义接口类型
interface QualityRule {
  ruleName: string
  checkField: string
  ruleType: string
  ruleConfig: string
  severity: string
}

interface TaskDetail {
  taskName: string
  description: string
  status: number
  sourceLayer: string
  targetLayer: string
  scheduleType: string
  cronExpression?: string
  targetTable: string
  writeMode: string
  createTime: string
  updateTime: string
  lastRunTime: string
  sourceTables: any[]
  fieldMappings: any[]
  qualityRules?: QualityRule[]
}

interface ExecutionHistory {
  executionId: string
  status: string
  startTime: string
  endTime: string
  duration: number
  processedRecords: number
  errorMessage: string
}

interface ExecutionMetrics {
  inputRecords: number
  outputRecords: number
  errorRecords: number
  skippedRecords: number
  avgProcessingRate: number
  memoryUsage: number
  cpuUsage: number
  networkIO: number
}

// 组件属性
interface Props {
  visible: boolean
  taskId: string | number | null
}

const props = defineProps<Props>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'edit', taskId: string | number): void
  (e: 'success'): void
}>();

const { proxy } = getCurrentInstance()!;
const { schedule_type } = (proxy as any).useDict('schedule_type');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const taskDetail: Ref<TaskDetail> = ref({} as TaskDetail);
const executionHistory: Ref<ExecutionHistory[]> = ref([]);
const historyLoading = ref(false);
const running = ref(false);
const logDialogVisible = ref(false);
const metricsDialogVisible = ref(false);
const executionLog = ref('');
const currentMetrics: Ref<ExecutionMetrics> = ref({} as ExecutionMetrics);

// 工具函数
const parseTime = (time: string | number | Date | null | undefined): string => {
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/** 获取任务详情 */
function getTaskDetail(): void {
  if (props.taskId) {
    getEtlTask(props.taskId).then((response: any) => {
      console.log(response);
      taskDetail.value = response.data || response;
    });
  }
}

/** 获取执行历史 */
function getExecutionHistoryData(): void {
  if (props.taskId) {
    historyLoading.value = true;
    fetchExecutionHistory(props.taskId).then((response: any) => {
      executionHistory.value = response.data || response || [];
    }).finally(() => {
      historyLoading.value = false;
    });
  }
}

/** 刷新执行历史 */
function refreshExecutionHistory(): void {
  getExecutionHistoryData();
}

/** 执行任务 */
function handleRun(): void {
  (proxy as any).$modal.confirm(`确认要执行"${taskDetail.value.taskName}"任务吗？`).then(() => {
    running.value = true;
    return runEtlTask(props.taskId);
  }).then(() => {
    (proxy as any).$modal.msgSuccess("任务执行成功");
    refreshExecutionHistory();
    emit('success');
  }).finally(() => {
    running.value = false;
  });
}

/** 编辑任务 */
function handleEdit(): void {
  if (props.taskId) {
    emit('edit', props.taskId);
  }
}

/** 关闭弹窗 */
function handleClose(): void {
  dialogVisible.value = false;
}

/** 查看执行日志 */
function handleViewLog(row: ExecutionHistory): void {
  getExecutionLog(row.executionId).then((response: any) => {
    console.log(response);
    executionLog.value = response.data || response || '';
    logDialogVisible.value = true;
  });
}

/** 查看执行指标 */
function handleViewMetrics(row: ExecutionHistory): void {
  getExecutionMetrics(row.executionId).then((response: any) => {
    console.log(response);
    currentMetrics.value = response.data || response || {};
    metricsDialogVisible.value = true;
  });
}

/** 关闭日志对话框 */
function handleCloseLogDialog(): void {
  logDialogVisible.value = false;
  executionLog.value = '';
}

/** 获取写入模式文本 */
function getWriteModeText(mode: string): string {
  const modeMap: Record<string, string> = {
    'OVERWRITE': '覆盖写入',
    'APPEND': '追加写入',
    'UPSERT': '更新插入'
  };
  return modeMap[mode] || mode;
}

/** 获取状态类型 */
function getStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'RUNNING': 'warning',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  };
  return typeMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status: string): string {
  const textMap: Record<string, string> = {
    'RUNNING': '运行中',
    'SUCCESS': '成功',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  };
  return textMap[status] || status;
}

/** 格式化执行时长 */
function formatDuration(duration: number): string {
  if (!duration) return '-';
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/** 获取质量规则类型颜色 */
function getQualityRuleTypeColor(ruleType: string): string {
  const colorMap: Record<string, string> = {
    'NOT_NULL': 'primary',
    'UNIQUE': 'success',
    'RANGE': 'warning',
    'FORMAT': 'info',
    'CUSTOM_SQL': 'danger'
  };
  return colorMap[ruleType] || 'info';
}

/** 获取质量规则类型文本 */
function getQualityRuleTypeText(ruleType: string): string {
  const textMap: Record<string, string> = {
    'NOT_NULL': '非空检查',
    'UNIQUE': '唯一性检查',
    'RANGE': '范围检查',
    'FORMAT': '格式检查',
    'CUSTOM_SQL': '自定义SQL'
  };
  return textMap[ruleType] || ruleType;
}

/** 获取严重级别颜色 */
function getSeverityTypeColor(severity: string): string {
  const colorMap: Record<string, string> = {
    'WARNING': 'warning',
    'ERROR': 'danger',
    'FATAL': 'danger'
  };
  return colorMap[severity] || 'info';
}

/** 获取严重级别文本 */
function getSeverityText(severity: string): string {
  const textMap: Record<string, string> = {
    'WARNING': '警告',
    'ERROR': '错误',
    'FATAL': '致命'
  };
  return textMap[severity] || severity;
}

/** 监听弹窗显示状态 */
watch(() => props.visible, (newVal) => {
  if (newVal && props.taskId) {
    getTaskDetail();
    getExecutionHistoryData();
  }
});
</script>

<style scoped>
.detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-container pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
