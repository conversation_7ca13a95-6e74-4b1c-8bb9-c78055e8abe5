# TaskDesignDialog 流程设计器使用说明

## 🎯 功能概述

TaskDesignDialog 是一个功能完整的可视化流程设计器，支持拖拽式设计数据采集任务流程。

## 🛠️ 主要功能

### 1. 左侧工具箱
- **数据源组件**：API调用、SQL查询、文件读取
- **数据处理组件**：数据转换、数据写入、数据过滤
- **控制流组件**：开始节点、结束节点、条件判断、循环节点

### 2. 中间画布
- **拖拽添加节点**：从工具箱拖拽组件到画布
- **节点移动**：拖拽节点改变位置
- **节点连接**：通过连接点创建数据流向
- **节点选择**：点击节点进行选择和配置

### 3. 右侧属性面板
- **节点配置**：选中节点后显示详细配置选项
- **动态组件**：根据节点类型加载对应的配置组件

## 📋 操作指南

### 基本操作

#### 1. 添加节点
**方法一：拖拽添加**
1. 从左侧工具箱选择组件
2. 拖拽到中间画布的目标位置
3. 松开鼠标完成添加

**方法二：点击添加**
1. 点击左侧工具箱中的组件
2. 节点会随机添加到画布中

#### 2. 移动节点
1. 在画布中点击并按住节点
2. 拖拽到目标位置
3. 松开鼠标完成移动

#### 3. 连接节点
1. 点击源节点右侧的输出连接点（蓝色圆点）
2. 拖拽到目标节点左侧的输入连接点
3. 松开鼠标创建连接线

#### 4. 配置节点
1. 点击选中目标节点
2. 在右侧属性面板中进行配置
3. 根据节点类型填写相应参数

#### 5. 删除节点
1. 选中要删除的节点
2. 点击顶部工具栏的"删除节点"按钮
3. 节点及其相关连接线将被删除

### 高级操作

#### 画布控制
- **放大**：点击"放大"按钮
- **缩小**：点击"缩小"按钮  
- **重置**：点击"重置"按钮恢复默认缩放

#### 流程操作
- **保存**：点击"保存"按钮保存流程设计
- **执行**：点击"执行"按钮运行任务（编辑模式）
- **预览**：点击"预览"按钮预览执行效果

## 🔧 节点类型说明

### 数据源节点

#### API调用节点
- **功能**：调用外部API接口获取数据
- **配置**：URL、请求方法、请求头、参数等
- **组件**：ApiCallNodeConfig.vue

#### SQL查询节点
- **功能**：执行SQL查询获取数据
- **配置**：数据源、SQL语句、参数等
- **组件**：SqlQueryNodeConfig.vue

#### 文件读取节点
- **功能**：读取文件数据
- **配置**：文件路径、格式、编码等

### 数据处理节点

#### 数据转换节点
- **功能**：对数据进行格式转换和处理
- **配置**：转换规则、输出格式等
- **组件**：DataTransformNodeConfig.vue

#### 数据写入节点
- **功能**：将数据写入目标存储
- **配置**：目标类型、连接信息等
- **组件**：DataWriteNodeConfig.vue

#### 数据过滤节点
- **功能**：根据条件过滤数据
- **配置**：过滤条件、规则等

### 控制流节点

#### 开始节点
- **功能**：流程的起始点
- **特点**：只有输出连接点
- **组件**：StartNodeConfig.vue

#### 结束节点
- **功能**：流程的结束点
- **特点**：只有输入连接点
- **组件**：EndNodeConfig.vue

#### 条件判断节点
- **功能**：根据条件分支执行
- **配置**：判断条件、分支逻辑
- **组件**：ConditionNodeConfig.vue

#### 循环节点
- **功能**：循环执行子流程
- **配置**：循环条件、次数等
- **组件**：LoopNodeConfig.vue

## 🎨 界面说明

### 顶部工具栏
```
[任务名称] | [保存] [执行] [预览] [关闭]
```

### 主体布局
```
┌─────────────┬──────────────────┬─────────────┐
│   工具箱    │      画布区域      │  属性面板   │
│             │                  │             │
│ 数据源      │   ┌─────┐        │ 节点名称    │
│ - API调用   │   │开始 │        │ 节点描述    │
│ - SQL查询   │   └─────┘        │             │
│             │      │           │ [配置组件]  │
│ 数据处理    │   ┌─────┐        │             │
│ - 数据转换  │   │API  │        │             │
│ - 数据写入  │   └─────┘        │             │
│             │      │           │             │
│ 控制流      │   ┌─────┐        │             │
│ - 条件判断  │   │结束 │        │             │
│ - 循环      │   └─────┘        │             │
└─────────────┴──────────────────┴─────────────┘
```

## 🔄 数据流向

### 连接规则
1. **开始节点**：只能作为源节点（只有输出）
2. **结束节点**：只能作为目标节点（只有输入）
3. **处理节点**：既可以作为源也可以作为目标
4. **一对多**：一个输出可以连接多个输入
5. **多对一**：多个输出可以连接一个输入

### 执行顺序
流程按照连接线的方向执行：
```
开始 → 数据源 → 数据处理 → 数据写入 → 结束
```

## 💡 使用技巧

### 1. 快速设计
- 先添加开始和结束节点
- 按照数据流向依次添加处理节点
- 最后创建连接线

### 2. 节点布局
- 保持节点间距适中，便于连线
- 按照逻辑顺序从左到右排列
- 复杂流程可以分层布局

### 3. 配置管理
- 及时保存节点配置
- 使用描述性的节点名称
- 添加必要的节点描述

### 4. 调试技巧
- 使用预览功能验证流程
- 检查所有连接是否正确
- 确保必要的配置已完成

## 🚀 扩展开发

### 添加新节点类型
1. 创建节点配置组件
2. 在工具箱中添加节点定义
3. 更新图标和配置映射
4. 实现节点的执行逻辑

### 自定义样式
- 修改CSS变量调整主题色彩
- 自定义节点样式和连接线样式
- 添加动画效果提升用户体验

这个流程设计器为数据采集任务提供了直观、易用的可视化设计界面，大大简化了复杂数据流程的设计和管理工作。
