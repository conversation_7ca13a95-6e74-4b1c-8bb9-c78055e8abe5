<template>
  <el-dialog
    :model-value="modelValue"
    :title="dialogTitle"
    width="600px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="分类名称" prop="categoryName">
        <el-input
          v-model="formData.categoryName"
          placeholder="请输入分类名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="分类编码" prop="categoryCode">
        <el-input
          v-model="formData.categoryCode"
          placeholder="请输入分类编码"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="父级分类" prop="parentId">
        <el-select
          v-model="formData.parentId"
          placeholder="请选择父级分类"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="option in parentOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="分类图标" prop="categoryIcon">
        <el-select
          v-model="formData.categoryIcon"
          placeholder="请选择分类图标"
          style="width: 100%"
        >
          <el-option
            v-for="icon in iconOptions"
            :key="icon.value"
            :label="icon.label"
            :value="icon.value"
          >
            <div style="display: flex; align-items: center;">
              <el-icon style="margin-right: 8px;">
                <component :is="icon.component" />
              </el-icon>
              {{ icon.label }}
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="0"
          :max="999"
          controls-position="right"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="分类描述" prop="categoryDesc">
        <el-input
          v-model="formData.categoryDesc"
          type="textarea"
          :rows="3"
          placeholder="请输入分类描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { addDataCollectionCategory, updateDataCollectionCategory } from '@/api/datacollection/category'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  categoryData: {
    type: Object,
    default: () => ({})
  },
  parentOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  categoryId: null,
  categoryName: '',
  categoryCode: '',
  parentId: 0,
  categoryIcon: 'folder',
  sortOrder: 0,
  status: 1,
  categoryDesc: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  categoryCode: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  categoryIcon: [
    { required: true, message: '请选择分类图标', trigger: 'change' }
  ]
}

// 图标选项
const iconOptions = [
  { value: 'folder', label: '文件夹', component: 'Folder' },
  { value: 'database', label: '数据库', component: 'Database' },
  { value: 'api', label: 'API接口', component: 'Connection' },
  { value: 'file', label: '文件', component: 'Document' },
  { value: 'sync', label: '同步', component: 'Refresh' },
  { value: 'mysql', label: 'MySQL', component: 'Database' },
  { value: 'sqlserver', label: 'SQL Server', component: 'Database' },
  { value: 'oracle', label: 'Oracle', component: 'Database' },
  { value: 'water', label: '水利', component: 'Drizzling' },
  { value: 'hydrology', label: '水文', component: 'Drizzling' },
  { value: 'quality', label: '水质', component: 'MagicStick' },
  { value: 'rain', label: '降雨', component: 'Drizzling' },
  { value: 'facility', label: '设施', component: 'OfficeBuilding' }
]

// 计算属性
const dialogTitle = computed(() => {
  return formData.categoryId ? '编辑分类' : '新增分类'
})

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.categoryData) {
    // 弹窗打开时更新表单数据
    const data = props.categoryData
    Object.assign(formData, {
      categoryId: data.categoryId || null,
      categoryName: data.categoryName || '',
      categoryCode: data.categoryCode || '',
      parentId: data.parentId !== undefined ? data.parentId : 0,
      categoryIcon: data.categoryIcon || 'folder',
      sortOrder: data.sortOrder || 0,
      status: data.status !== undefined ? data.status : 1,
      categoryDesc: data.categoryDesc || '',
      remark: data.remark || ''
    })
  }
})

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true
    
    // 提交数据
    if (formData.categoryId) {
      // 更新
      await updateDataCollectionCategory(formData)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await addDataCollectionCategory(formData)
      ElMessage.success('新增成功')
    }
    
    // 触发成功事件
    emit('success')
    
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
}
</style>
