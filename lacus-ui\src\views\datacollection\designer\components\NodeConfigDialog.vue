<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    :close-on-click-modal="false"
    class="node-config-dialog"
    @close="handleClose"
  >
    <!-- 动态加载对应的节点配置组件 -->
    <component 
      v-if="nodeType"
      :is="getConfigComponent(nodeType)"
      v-model="localConfig"
      :mode="mode"
    />
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import ApiCallNodeConfig from './ApiCallNodeConfig.vue'
import SqlQueryNodeConfig from './SqlQueryNodeConfig.vue'
import DataTransformNodeConfig from './DataTransformNodeConfig.vue'
import DataWriteNodeConfig from './DataWriteNodeConfig.vue'
import ConditionNodeConfig from './ConditionNodeConfig.vue'
import LoopNodeConfig from './LoopNodeConfig.vue'
import StartNodeConfig from './StartNodeConfig.vue'
import EndNodeConfig from './EndNodeConfig.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  nodeType: {
    type: String,
    default: ''
  },
  nodeConfig: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'add' // add, edit
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const localConfig = ref({})

// 监听配置变化
watch(() => props.nodeConfig, (newConfig) => {
  localConfig.value = { ...newConfig }
}, { immediate: true, deep: true })

// 计算属性
const dialogTitle = computed(() => {
  const typeNames = {
    'api-call': 'API调用',
    'sql-query': 'SQL查询',
    'file-read': '文件读取',
    'data-transform': '数据转换',
    'data-write': '数据写入',
    'data-filter': '数据过滤',
    'start': '开始节点',
    'end': '结束节点',
    'condition': '条件判断',
    'loop': '循环节点'
  }
  
  const typeName = typeNames[props.nodeType] || '节点'
  return `${props.mode === 'add' ? '配置' : '编辑'} - ${typeName}`
})

// 获取配置组件
function getConfigComponent(nodeType) {
  const componentMap = {
    'api-call': ApiCallNodeConfig,
    'sql-query': SqlQueryNodeConfig,
    'data-transform': DataTransformNodeConfig,
    'data-write': DataWriteNodeConfig,
    'condition': ConditionNodeConfig,
    'loop': LoopNodeConfig,
    'start': StartNodeConfig,
    'end': EndNodeConfig,
    'file-read': ApiCallNodeConfig, // 临时使用API调用组件
    'data-filter': ConditionNodeConfig // 临时使用条件组件
  }
  
  return componentMap[nodeType] || StartNodeConfig
}

// 处理保存
function handleSave() {
  emit('save', localConfig.value)
}

// 处理关闭
function handleClose() {
  dialogVisible.value = false
}
</script>

<style scoped>
.node-config-dialog {
  .el-dialog__body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }
}
</style>
