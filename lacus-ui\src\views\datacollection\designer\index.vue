<template>
  <div class="data-collection-container">
    <!-- 左侧分类树 -->
    <div class="category-panel">
      <div class="category-header">
        <h3>任务分类目录</h3>
        <el-button
          type="primary"
          size="small"
          icon="Plus"
          @click="handleAddCategory"
          v-hasPermission="['datacollection:category:add']"
        >
          新增分类
        </el-button>
      </div>

      <div class="category-tree">
        <el-tree
          ref="categoryTreeRef"
          :data="categoryTree"
          :props="treeProps"
          :expand-on-click-node="false"
          :default-expand-all="true"
          node-key="categoryId"
          highlight-current
          @node-click="handleCategoryClick"
          @node-contextmenu="handleCategoryRightClick"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon class="node-icon">
                <component :is="getCategoryIcon(data.categoryIcon)" />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
              <span class="node-count">({{ data.taskCount || 0 }})</span>
              <div class="node-actions" v-if="data.categoryId !== 1">
                <el-button
                  type="text"
                  size="small"
                  icon="Edit"
                  @click.stop="handleEditCategory(data)"
                  title="编辑分类"
                />
                <el-button
                  type="text"
                  size="small"
                  icon="Delete"
                  @click.stop="handleDeleteCategory(data)"
                  title="删除分类"
                />
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧任务列表 -->
    <div class="task-panel">
      <div class="task-header">
        <div class="header-left">
          <h3>{{ currentCategoryName }}</h3>
          <el-breadcrumb separator="/" v-if="breadcrumbItems.length > 0">
            <el-breadcrumb-item
              v-for="item in breadcrumbItems"
              :key="item.categoryId"
              @click="handleBreadcrumbClick(item)"
              class="breadcrumb-item"
            >
              {{ item.categoryName }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-button
            type="primary"
            icon="Plus"
            @click="handleAddTask"
            v-hasPermission="['datacollection:task:add']"
          >
            新增任务
          </el-button>
          <el-button
            icon="Refresh"
            @click="refreshTaskList"
          >
            刷新
          </el-button>
        </div>
      </div>

      <!-- 搜索条件 -->
      <div class="search-panel" v-show="showSearch">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              v-model="queryParams.taskName"
              placeholder="请输入任务名称"
              clearable
              @keyup.enter="handleQuery"
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="采集类型" prop="collectionType">
            <el-select v-model="queryParams.collectionType" placeholder="请选择采集类型" clearable style="width: 150px;">
              <el-option label="API接口" value="API" />
              <el-option label="SQL查询" value="SQL" />
              <el-option label="文件采集" value="FILE" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务列表 -->
      <div class="task-list">
        <el-table 
          v-loading="loading" 
          :data="taskList" 
          @selection-change="handleSelectionChange"
          height="calc(100vh - 280px)"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="编号" align="center" prop="taskId" width="80" />
          <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" min-width="200" />
          <el-table-column label="任务描述" align="center" prop="taskDesc" :show-overflow-tooltip="true" min-width="200" />
          <el-table-column label="采集类型" align="center" prop="collectionType" width="100">
            <template #default="scope">
              <el-tag :type="getCollectionTypeTag(scope.row.collectionType)">
                {{ getCollectionTypeText(scope.row.collectionType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="写入模式" align="center" prop="writeMode" width="100">
            <template #default="scope">
              <el-tag :type="getWriteModeTag(scope.row.writeMode)">
                {{ getWriteModeText(scope.row.writeMode) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="调度类型" align="center" prop="scheduleType" width="100">
            <template #default="scope">
              <el-tag :type="getScheduleTypeTag(scope.row.scheduleType)">
                {{ getScheduleTypeText(scope.row.scheduleType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="80">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
            <template #default="scope">
              <el-button
                type="text"
                icon="Edit"
                @click="handleEdit(scope.row)"
                v-hasPermission="['datacollection:task:edit']"
              >修改</el-button>
              <el-button
                type="text"
                icon="VideoPlay"
                @click="handleExecute(scope.row)"
                v-hasPermission="['datacollection:task:execute']"
                :disabled="scope.row.status !== 1"
              >执行</el-button>
              <el-dropdown @command="(command) => handleCommand(command, scope.row)">
                <el-button type="text" icon="More">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="detail" icon="View">详情</el-dropdown-item>
                    <el-dropdown-item command="copy" icon="CopyDocument">复制</el-dropdown-item>
                    <el-dropdown-item command="history" icon="Clock">执行历史</el-dropdown-item>
                    <el-dropdown-item command="move" icon="Folder">移动分类</el-dropdown-item>
                    <el-dropdown-item command="delete" icon="Delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getTaskList"
        />
      </div>
    </div>

    <!-- 分类右键菜单 -->
    <el-dropdown
      ref="categoryContextMenuRef"
      trigger="contextmenu"
      @command="handleCategoryCommand"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="addSubCategory" icon="Plus">新增子分类</el-dropdown-item>
          <el-dropdown-item command="editCategory" icon="Edit">编辑分类</el-dropdown-item>
          <el-dropdown-item command="deleteCategory" icon="Delete" divided>删除分类</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      :title="isEditCategory ? '编辑分类' : '新增分类'"
      width="600px"
      @close="handleCategoryDialogClose"
    >
    <h1>123</h1>
      <!-- <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="categoryForm.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="categoryCode">
          <el-input v-model="categoryForm.categoryCode" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="父级分类" prop="parentId">
          <el-select v-model="categoryForm.parentId" placeholder="请选择父级分类" style="width: 100%">
            <el-option
              v-for="option in categoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类图标" prop="categoryIcon">
          <el-select v-model="categoryForm.categoryIcon" placeholder="请选择分类图标" style="width: 100%">
            <el-option value="folder" label="文件夹" />
            <el-option value="database" label="数据库" />
            <el-option value="api" label="API接口" />
            <el-option value="file" label="文件" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="categoryForm.sortOrder" :min="0" :max="999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="categoryForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分类描述" prop="categoryDesc">
          <el-input
            v-model="categoryForm.categoryDesc"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
      </el-form> -->

      <template #footer>
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCategorySubmit" :loading="categorySubmitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 任务移动对话框 -->
    <!-- <TaskMoveDialog
      v-model="moveDialogVisible"
      :task-data="currentTask"
      :category-options="categoryOptions"
      @success="handleMoveSuccess"
    /> -->
  </div>
</template>

<script setup lang="ts" name="DataCollectionIndex">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import type { ElTree } from 'element-plus'
import {
  listDataCollectionTask,
  delDataCollectionTask,
  changeTaskStatus,
  executeTask
} from '@/api/datacollection/task'
import {
  getCategoryTree,
  delDataCollectionCategory,
  addDataCollectionCategory,
  updateDataCollectionCategory
} from '@/api/datacollection/category'

import Pagination from '@/components/Pagination/index.vue'
import { parseTime } from '@/utils/dateUtil'
import { useRouter } from 'vue-router'

// 类型定义
interface CategoryData {
  categoryId: number
  categoryName: string
  categoryCode: string
  parentId: number
  categoryIcon: string
  sortOrder: number
  status: number
  categoryDesc: string
  taskCount: number
  remark?: string
  children?: CategoryData[]
}

interface TaskData {
  taskId: number
  taskName: string
  taskCode: string
  categoryId: number
  taskDesc: string
  collectionType: string
  sourceType: string
  writeMode: string
  scheduleType: string
  status: number
  createTime: string
  updateTime: string
}

interface QueryParams {
  pageNum: number
  pageSize: number
  taskName: string | null
  collectionType: string | null
  status: number | null
  categoryId: number | null
}

interface BreadcrumbItem {
  categoryId: number
  categoryName: string
}

const router = useRouter()

// 响应式数据
const loading = ref<boolean>(true)
const showSearch = ref<boolean>(true)
const total = ref<number>(0)
const taskList = ref<TaskData[]>([])
const categoryTree = ref<CategoryData[]>([])
const categoryOptions = ref<any[]>([])
const breadcrumbItems = ref<BreadcrumbItem[]>([])
const currentCategoryId = ref<number | null>(null)
const currentCategoryName = ref<string>('请选择分类')
const currentCategory = ref<Partial<CategoryData>>({})
const currentTask = ref<Partial<TaskData>>({})
const categoryDialogVisible = ref<boolean>(false)
const moveDialogVisible = ref<boolean>(false)
const selectedTasks = ref<TaskData[]>([])
const isEditCategory = ref<boolean>(false)
const categorySubmitting = ref<boolean>(false)

// 分类表单数据
const categoryForm = reactive({
  categoryId: null as number | null,
  categoryName: '',
  categoryCode: '',
  parentId: 0,
  categoryIcon: 'folder',
  sortOrder: 0,
  status: 1,
  categoryDesc: ''
})

// 分类表单验证规则
const categoryRules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  categoryCode: [
    { required: true, message: '请输入分类编码', trigger: 'blur' }
  ]
}

// 查询参数
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  taskName: null,
  collectionType: null,
  status: null,
  categoryId: null
})

// 树形组件属性
const treeProps = {
  children: 'children',
  label: 'categoryName',
  value: 'categoryId'
}

// 组件引用
const categoryTreeRef = ref<InstanceType<typeof ElTree>>()
const categoryContextMenuRef = ref()
const queryRef = ref()

// 生命周期
onMounted(() => {
  loadCategoryTree()
  getTaskList()
})

// 获取分类树
const loadCategoryTree = async (): Promise<void> => {
  try {
    const response = await getCategoryTree()
    console.log('分类树数据:', response)

    // 后端返回的是树形结构，直接使用
    categoryTree.value = response as unknown as CategoryData[]
    categoryOptions.value = buildCategoryOptions(response as unknown as CategoryData[])
  } catch (error) {
    console.error('获取分类树失败:', error)
    categoryTree.value = []
    categoryOptions.value = []
  }
}

// 获取任务列表
const getTaskList = async (): Promise<void> => {
  loading.value = true
  try {
    const response = await listDataCollectionTask(queryParams)
    // setup语法糖会自动取data值
    taskList.value = (response as any).rows || (response as any) || []
    total.value = (response as any).total || 0
  } catch (error) {
    console.error('获取任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理分类点击
const handleCategoryClick = (data: CategoryData): void => {
  currentCategoryId.value = data.categoryId
  currentCategoryName.value = data.categoryName
  queryParams.categoryId = data.categoryId
  queryParams.pageNum = 1

  // 构建面包屑
  buildBreadcrumb(data)

  // 刷新任务列表
  getTaskList()
}

// 构建面包屑
const buildBreadcrumb = (categoryData: CategoryData): void => {
  const items: BreadcrumbItem[] = []
  let current: CategoryData | null = categoryData

  while (current) {
    items.unshift({
      categoryId: current.categoryId,
      categoryName: current.categoryName
    })
    current = findParentCategory(current.parentId)
  }

  breadcrumbItems.value = items
}

// 查找父分类
const findParentCategory = (parentId: number): CategoryData | null => {
  if (!parentId || parentId === 0) return null

  const findInTree = (nodes: CategoryData[]): CategoryData | null => {
    for (const node of nodes) {
      if (node.categoryId === parentId) {
        return node
      }
      if (node.children) {
        const found = findInTree(node.children)
        if (found) return found
      }
    }
    return null
  }

  return findInTree(categoryTree.value)
}

// 面包屑点击
const handleBreadcrumbClick = (item: BreadcrumbItem): void => {
  const node = categoryTreeRef.value?.getNode(item.categoryId)
  if (node) {
    categoryTreeRef.value?.setCurrentKey(item.categoryId)
    handleCategoryClick(node.data as CategoryData)
  }
}

// 分类右键菜单
const handleCategoryRightClick = (event: MouseEvent, data: CategoryData): void => {
  event.preventDefault()
  currentCategory.value = data

  nextTick(() => {
    categoryContextMenuRef.value?.handleOpen()
  })
}

// 分类命令处理
const handleCategoryCommand = (command: string): void => {
  switch (command) {
    case 'addSubCategory':
      handleAddSubCategory()
      break
    case 'editCategory':
      handleEditCategory()
      break
    case 'deleteCategory':
      handleDeleteCategory()
      break
  }
}

// 新增分类
const handleAddCategory = (): void => {
  isEditCategory.value = false
  Object.assign(categoryForm, {
    categoryId: null,
    categoryName: '',
    categoryCode: '',
    parentId: currentCategoryId.value || 0,
    categoryIcon: 'folder',
    sortOrder: 0,
    status: 1,
    categoryDesc: ''
  })
  // categoryDialogVisible.value = true
  nextTick(() => {
    categoryDialogVisible.value = true
  })
}

// 新增子分类
const handleAddSubCategory = (): void => {
  isEditCategory.value = false
  Object.assign(categoryForm, {
    categoryId: null,
    categoryName: '',
    categoryCode: '',
    parentId: currentCategory.value.categoryId || 0,
    categoryIcon: 'folder',
    sortOrder: 0,
    status: 1,
    categoryDesc: ''
  })
  categoryDialogVisible.value = true
}

// 编辑分类
const handleEditCategory = (data?: CategoryData): void => {
  isEditCategory.value = true
  if (data) {
    Object.assign(categoryForm, {
      categoryId: data.categoryId,
      categoryName: data.categoryName,
      categoryCode: data.categoryCode,
      parentId: data.parentId,
      categoryIcon: data.categoryIcon,
      sortOrder: data.sortOrder,
      status: data.status,
      categoryDesc: data.categoryDesc
    })
  }
  categoryDialogVisible.value = true
}

// 删除分类
const handleDeleteCategory = async (data?: CategoryData): Promise<void> => {
  try {
    const categoryToDelete = data || currentCategory.value

    // 检查是否有任务
    if (categoryToDelete.taskCount && categoryToDelete.taskCount > 0) {
      ElMessage.warning(`分类"${categoryToDelete.categoryName}"下还有${categoryToDelete.taskCount}个任务，无法删除`)
      return
    }

    // 检查是否有子分类
    if (categoryToDelete.children && categoryToDelete.children.length > 0) {
      const hasTasksInChildren = checkChildrenHasTasks(categoryToDelete.children)
      if (hasTasksInChildren) {
        ElMessage.warning(`分类"${categoryToDelete.categoryName}"的子分类下还有任务，无法删除`)
        return
      }
    }

    await ElMessageBox.confirm(
      `是否确认删除分类"${categoryToDelete.categoryName}"？${categoryToDelete.children && categoryToDelete.children.length > 0 ? '（包括所有子分类）' : ''}`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await delDataCollectionCategory(categoryToDelete.categoryId!)
    ElMessage.success('删除成功')
    loadCategoryTree()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
    }
  }
}

// 检查子分类是否有任务
const checkChildrenHasTasks = (children: CategoryData[]): boolean => {
  for (const child of children) {
    if (child.taskCount && child.taskCount > 0) {
      return true
    }
    if (child.children && child.children.length > 0) {
      if (checkChildrenHasTasks(child.children)) {
        return true
      }
    }
  }
  return false
}

// 分类对话框关闭
const handleCategoryDialogClose = (): void => {
  // 重置表单
  Object.assign(categoryForm, {
    categoryId: null,
    categoryName: '',
    categoryCode: '',
    parentId: 0,
    categoryIcon: 'folder',
    sortOrder: 0,
    status: 1,
    categoryDesc: ''
  })
  isEditCategory.value = false
}

// 分类表单提交
const handleCategorySubmit = async (): Promise<void> => {
  categorySubmitting.value = true
  try {
    if (isEditCategory.value) {
      await updateDataCollectionCategory(categoryForm)
      ElMessage.success('修改成功')
    } else {
      await addDataCollectionCategory(categoryForm)
      ElMessage.success('新增成功')
    }
    categoryDialogVisible.value = false
    loadCategoryTree()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    categorySubmitting.value = false
  }
}



// 获取分类图标
const getCategoryIcon = (iconName: string): string => {
  const iconMap: Record<string, string> = {
    folder: 'Folder',
    database: 'Database',
    api: 'Connection',
    file: 'Document',
    sync: 'Refresh',
    mysql: 'Database',
    sqlserver: 'Database',
    oracle: 'Database',
    water: 'Drizzling',
    hydrology: 'Drizzling',
    quality: 'MagicStick',
    rain: 'Drizzling',
    facility: 'OfficeBuilding'
  }
  return iconMap[iconName] || 'Folder'
}

// 搜索
const handleQuery = (): void => {
  queryParams.pageNum = 1
  getTaskList()
}

// 重置搜索
const resetQuery = (): void => {
  queryRef.value?.resetFields()
  handleQuery()
}

// 刷新任务列表
const refreshTaskList = (): void => {
  getTaskList()
}

// 新增任务
const handleAddTask = (): void => {
  if (!currentCategoryId.value) {
    ElMessage.warning('请先选择分类')
    return
  }

  // 跳转到流程设计页面进行新增
  router.push({
    path: '/datacollection/task/design',
    query: {
      mode: 'add',
      categoryId: currentCategoryId.value.toString()
    }
  })
}

// 修改任务
const handleEdit = (row: TaskData): void => {
  // 跳转到流程设计页面进行修改
  router.push({
    path: '/datacollection/task/design',
    query: {
      mode: 'edit',
      taskId: row.taskId.toString()
    }
  })
}

// 任务详情
const handleDetail = (row: TaskData): void => {
  // 跳转到流程设计页面查看详情
  router.push({
    path: '/datacollection/task/design',
    query: {
      mode: 'view',
      taskId: row.taskId.toString()
    }
  })
}



// 执行任务
const handleExecute = async (row: TaskData): Promise<void> => {
  try {
    await ElMessageBox.confirm(
      `是否确认执行任务"${row.taskName}"？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await executeTask(row.taskId)
    ElMessage.success('任务执行成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行任务失败:', error)
    }
  }
}

// 状态变更
const handleStatusChange = async (row: TaskData): Promise<void> => {
  try {
    const text = row.status === 1 ? '启用' : '停用'
    await ElMessageBox.confirm(
      `确认要${text}"${row.taskName}"任务吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await changeTaskStatus(row.taskId, row.status)
    ElMessage.success(`${text}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      row.status = row.status === 0 ? 1 : 0
      console.error('状态变更失败:', error)
    }
  }
}

// 更多操作
const handleCommand = (command: string, row: TaskData): void => {
  currentTask.value = row

  switch (command) {
    case 'detail':
      handleDetail(row)
      break
    case 'copy':
      handleCopyTask()
      break
    case 'history':
      handleTaskHistory()
      break
    case 'move':
      handleMoveTask()
      break
    case 'delete':
      handleDeleteTask()
      break
  }
}

// 复制任务
const handleCopyTask = (): void => {
  router.push({
    path: '/datacollection/task/design',
    query: {
      mode: 'copy',
      taskId: currentTask.value.taskId?.toString(),
      categoryId: currentCategoryId.value?.toString()
    }
  })
}

// 任务历史
const handleTaskHistory = (): void => {
  router.push({
    path: '/datacollection/history',
    query: {
      taskId: currentTask.value.taskId?.toString()
    }
  })
}

// 移动任务
const handleMoveTask = (): void => {
  moveDialogVisible.value = true
}

// 删除任务
const handleDeleteTask = async (): Promise<void> => {
  try {
    await ElMessageBox.confirm(
      `是否确认删除任务"${currentTask.value.taskName}"？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await delDataCollectionTask(currentTask.value.taskId!)
    ElMessage.success('删除成功')
    getTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
    }
  }
}



// 多选变化
const handleSelectionChange = (selection: TaskData[]): void => {
  selectedTasks.value = selection
}

// 获取采集类型标签
const getCollectionTypeTag = (type: string): string => {
  const tagMap: Record<string, string> = {
    'API': 'success',
    'SQL': 'primary',
    'FILE': 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取采集类型文本
const getCollectionTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    'API': 'API接口',
    'SQL': 'SQL查询',
    'FILE': '文件采集'
  }
  return textMap[type] || type
}

// 获取写入模式标签
const getWriteModeTag = (mode: string): string => {
  const tagMap: Record<string, string> = {
    'OVERWRITE': 'danger',
    'APPEND': 'success',
    'UPSERT': 'warning',
    'AUTO_CREATE': 'info'
  }
  return tagMap[mode] || 'info'
}

// 获取写入模式文本
const getWriteModeText = (mode: string): string => {
  const textMap: Record<string, string> = {
    'OVERWRITE': '覆盖',
    'APPEND': '追加',
    'UPSERT': '更新插入',
    'AUTO_CREATE': '自动建表'
  }
  return textMap[mode] || mode
}

// 获取调度类型标签
const getScheduleTypeTag = (type: string): string => {
  const tagMap: Record<string, string> = {
    'MANUAL': 'info',
    'CRON': 'success',
    'REALTIME': 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取调度类型文本
const getScheduleTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    'MANUAL': '手动',
    'CRON': '定时',
    'REALTIME': '实时'
  }
  return textMap[type] || type
}



// 构建分类选项
const buildCategoryOptions = (data: CategoryData[]): any[] => {
  const options: any[] = []

  // 添加根选项
  options.push({
    value: 0,
    label: '根目录',
    disabled: false
  })

  const buildOptions = (nodes: CategoryData[], level = 0): void => {
    if (!Array.isArray(nodes)) return

    for (const node of nodes) {
      options.push({
        value: node.categoryId,
        label: '　'.repeat(level + 1) + node.categoryName,
        disabled: false
      })

      if (node.children && Array.isArray(node.children) && node.children.length > 0) {
        buildOptions(node.children, level + 1)
      }
    }
  }

  if (Array.isArray(data)) {
    buildOptions(data)
  }
  return options
}
</script>

<style scoped>
.data-collection-container {
  display: flex;
  height: calc(100vh - 84px);
  background: #f5f7fa;
  gap: 16px;
  padding: 16px;
}

.category-panel {
  width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.category-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.category-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.category-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 32px;
}

.tree-node:hover {
  background-color: #f8f9ff;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.node-icon {
  margin-right: 8px;
  color: #606266;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.node-count {
  color: #909399;
  font-size: 12px;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
  flex-shrink: 0;
  margin-right: 8px;
}

.node-actions {
  opacity: 0;
  display: flex;
  gap: 2px;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.node-actions .el-button {
  padding: 2px 4px;
  min-height: 20px;
  border: none;
  font-size: 12px;
}

.node-actions .el-button:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

.task-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.task-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #fff;
}

.header-left h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-left .el-breadcrumb {
  margin-top: 8px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.search-panel {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

.task-list {
  flex: 1;
  padding: 16px 20px;
  overflow: hidden;
}

/* 树形组件样式优化 */
:deep(.el-tree-node__content) {
  height: 40px;
  border-radius: 6px;
  margin: 2px 0;
  transition: all 0.2s ease;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f8f9ff;
}

:deep(.el-tree-node__expand-icon) {
  color: #606266;
  font-size: 14px;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 600;
}

/* 面包屑样式 */
:deep(.el-breadcrumb__item) {
  cursor: pointer;
}

:deep(.el-breadcrumb__item:hover .el-breadcrumb__inner) {
  color: #409eff;
}

:deep(.el-breadcrumb__inner) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-breadcrumb__separator) {
  color: #c0c4cc;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafbfc;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f5f5f5;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9ff;
}

/* 按钮样式优化 */
.el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

.el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 12px;
  font-weight: 500;
}

/* 开关样式优化 */
:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #67c23a;
}

/* 滚动条样式 */
.category-tree::-webkit-scrollbar,
.task-list::-webkit-scrollbar {
  width: 6px;
}

.category-tree::-webkit-scrollbar-track,
.task-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.category-tree::-webkit-scrollbar-thumb,
.task-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.category-tree::-webkit-scrollbar-thumb:hover,
.task-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
