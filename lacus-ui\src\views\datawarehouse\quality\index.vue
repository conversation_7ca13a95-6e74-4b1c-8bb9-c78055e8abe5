<template>
  <div class="app-container">
    <!-- 数据质量概览 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card class="quality-card">
          <div class="quality-item">
            <div class="quality-value">{{ qualityStats.totalTables }}</div>
            <div class="quality-label">监控表数量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="quality-card">
          <div class="quality-item">
            <div class="quality-value success">{{ qualityStats.passedTables }}</div>
            <div class="quality-label">质量合格表</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="quality-card">
          <div class="quality-item">
            <div class="quality-value warning">{{ qualityStats.warningTables }}</div>
            <div class="quality-label">质量警告表</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="quality-card">
          <div class="quality-item">
            <div class="quality-value danger">{{ qualityStats.failedTables }}</div>
            <div class="quality-label">质量异常表</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 质量趋势图表 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>数据质量趋势</span>
          <el-radio-group v-model="trendPeriod" @change="loadTrendData">
            <el-radio-button label="7d">近7天</el-radio-button>
            <el-radio-button label="30d">近30天</el-radio-button>
            <el-radio-button label="90d">近90天</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div id="quality-trend-chart" ref="trendChart" style="width: 100%; height: 300px;"></div>
    </el-card>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="表名" prop="tableName">
        <el-input v-model="queryParams.tableName" placeholder="请输入表名" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="数据层级" prop="dataLayer">
        <el-select v-model="queryParams.dataLayer" placeholder="请选择数据层级" clearable>
          <el-option label="ODS" value="ODS"/>
          <el-option label="DWD" value="DWD"/>
          <el-option label="DWS" value="DWS"/>
          <el-option label="ADS" value="ADS"/>
        </el-select>
      </el-form-item>
      <el-form-item label="质量状态" prop="qualityStatus">
        <el-select v-model="queryParams.qualityStatus" placeholder="请选择质量状态" clearable>
          <el-option label="正常" value="PASSED"/>
          <el-option label="警告" value="WARNING"/>
          <el-option label="异常" value="FAILED"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAddRule">新增质量规则</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="VideoPlay" @click="handleBatchCheck" :disabled="multiple">批量检查</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Download" @click="handleExport">导出报告</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="qualityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="表名" align="center" prop="tableName" :show-overflow-tooltip="true"/>
      <el-table-column label="数据层级" align="center" prop="dataLayer" width="100">
        <template #default="scope">
          <el-tag :type="getLayerTagType(scope.row.dataLayer)">{{ scope.row.dataLayer }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="质量得分" align="center" prop="qualityScore" width="120">
        <template #default="scope">
          <el-progress
            :percentage="scope.row.qualityScore"
            :color="getScoreColor(scope.row.qualityScore)"
            :show-text="true"
            :stroke-width="8"
          />
        </template>
      </el-table-column>
      <el-table-column label="质量状态" align="center" prop="qualityStatus" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.qualityStatus)">
            {{ getStatusText(scope.row.qualityStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="规则数量" align="center" prop="ruleCount" width="100"/>
      <el-table-column label="异常规则" align="center" prop="failedRuleCount" width="100">
        <template #default="scope">
          <span :class="scope.row.failedRuleCount > 0 ? 'text-danger' : ''">
            {{ scope.row.failedRuleCount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="记录数" align="center" prop="recordCount" width="120"/>
      <el-table-column label="最后检查时间" align="center" prop="lastCheckTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastCheckTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button-group>
            <el-tooltip content="查看详情" placement="top">
              <el-button type="primary" icon="View" @click="handleDetail(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="质量检查" placement="top">
              <el-button type="success" icon="VideoPlay" @click="handleCheck(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="配置规则" placement="top">
              <el-button type="warning" icon="Setting" @click="handleConfigRule(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="查看报告" placement="top">
              <el-button type="info" icon="Document" @click="handleReport(scope.row)" size="small"/>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 质量监控创建/编辑弹出层 -->
    <QualityMonitorCreate
      v-model:visible="createDialogVisible"
      :monitor-id="currentMonitorId"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts" name="DataQuality">
import { ref, reactive, toRefs, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import { listQualityMonitor, runQualityCheck, getQualityStats, getQualityTrend } from '@/api/datawarehouse/quality';
import QualityMonitorCreate from './create.vue';
import * as echarts from 'echarts';

// 定义接口类型
interface QualityStats {
  totalTables: number
  passedTables: number
  warningTables: number
  failedTables: number
}

interface QualityMonitor {
  id: string | number
  tableName: string
  dataLayer: string
  qualityScore: number
  qualityStatus: string
  ruleCount: number
  failedRuleCount: number
  recordCount: number
  lastCheckTime: string
}

const { proxy } = getCurrentInstance()!;
const router = useRouter();

const qualityList: Ref<QualityMonitor[]> = ref([]);
const qualityStats: Ref<QualityStats> = ref({
  totalTables: 0,
  passedTables: 0,
  warningTables: 0,
  failedTables: 0
});
const loading = ref(true);
const showSearch = ref(true);
const ids: Ref<(string | number)[]> = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const trendPeriod = ref('7d');
const trendChart = ref(null);
const createDialogVisible = ref(false);
const currentMonitorId: Ref<string | number | null> = ref(null);
let chartInstance: any = null;

// 查询参数接口
interface QueryParams {
  pageNum: number
  pageSize: number
  tableName?: string
  dataLayer?: string
  qualityStatus?: string
}

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tableName: undefined,
    dataLayer: undefined,
    qualityStatus: undefined
  } as QueryParams
});

const { queryParams } = toRefs(data);

/** 查询质量监控列表 */
function getList(): void {
  loading.value = true;
  listQualityMonitor(queryParams.value).then((response: any) => {
    qualityList.value = response.rows || response.data?.rows || [];
    total.value = response.total || response.data?.total || 0;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 获取质量统计 */
function getStats(): void {
  getQualityStats().then((response: any) => {
    qualityStats.value = response.data || response || {
      totalTables: 0,
      passedTables: 0,
      warningTables: 0,
      failedTables: 0
    };
  });
}

/** 加载趋势数据 */
function loadTrendData(): void {
  getQualityTrend(trendPeriod.value).then((response: any) => {
    renderTrendChart(response.data || response);
  });
}

/** 渲染趋势图表 */
function renderTrendChart(data: any): void {
  if (!chartInstance) {
    chartInstance = echarts.init(trendChart.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['合格率', '警告率', '异常率']
    },
    xAxis: {
      type: 'category',
      data: data.dates || []
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '合格率',
        type: 'line',
        data: data.passedRates || [],
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '警告率',
        type: 'line',
        data: data.warningRates || [],
        itemStyle: { color: '#E6A23C' }
      },
      {
        name: '异常率',
        type: 'line',
        data: data.failedRates || [],
        itemStyle: { color: '#F56C6C' }
      }
    ]
  };

  chartInstance.setOption(option);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增质量规则 */
function handleAddRule() {
  currentMonitorId.value = null;
  createDialogVisible.value = true;
}

/** 配置规则 */
function handleConfigRule(row) {
  currentMonitorId.value = row.id || ids.value;
  createDialogVisible.value = true;
}

/** 创建/编辑成功回调 */
function handleCreateSuccess() {
  createDialogVisible.value = false;
  getList();
}

/** 查看详情 */
function handleDetail(row) {
  router.push(`/datawarehouse/quality/detail/${row.id}`);
}

/** 质量检查 */
function handleCheck(row) {
  proxy.$modal.confirm(`确认要对表"${row.tableName}"进行质量检查吗？`).then(() => {
    return runQualityCheck(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("质量检查已启动");
    getList();
  });
}

/** 批量检查 */
function handleBatchCheck() {
  proxy.$modal.confirm(`确认要对选中的${ids.value.length}个表进行质量检查吗？`).then(() => {
    return runQualityCheck(ids.value.join(','));
  }).then(() => {
    proxy.$modal.msgSuccess("批量质量检查已启动");
    getList();
  });
}

/** 查看报告 */
function handleReport(row) {
  router.push(`/datawarehouse/quality/report/${row.id}`);
}

/** 导出报告 */
function handleExport() {
  proxy.$modal.msgInfo('导出功能开发中...');
}

/** 获取层级标签类型 */
function getLayerTagType(layer) {
  const typeMap = {
    'ODS': 'success',
    'DWD': 'primary',
    'DWS': 'warning',
    'ADS': 'danger'
  };
  return typeMap[layer] || 'info';
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  const typeMap = {
    'PASSED': 'success',
    'WARNING': 'warning',
    'FAILED': 'danger'
  };
  return typeMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status) {
  const textMap = {
    'PASSED': '正常',
    'WARNING': '警告',
    'FAILED': '异常'
  };
  return textMap[status] || status;
}

/** 获取得分颜色 */
function getScoreColor(score) {
  if (score >= 90) return '#67C23A';
  if (score >= 70) return '#E6A23C';
  return '#F56C6C';
}

onMounted(() => {
  getList();
  getStats();
  loadTrendData();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped>
.quality-card {
  text-align: center;
}

.quality-item {
  padding: 20px 0;
}

.quality-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
}

.quality-value.success {
  color: #67C23A;
}

.quality-value.warning {
  color: #E6A23C;
}

.quality-value.danger {
  color: #F56C6C;
}

.quality-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}
</style>
