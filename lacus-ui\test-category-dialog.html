<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类对话框测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <div id="app">
        <el-button type="primary" @click="showDialog">测试分类对话框</el-button>
        
        <el-dialog
            v-model="dialogVisible"
            title="测试对话框"
            width="600px"
        >
            <el-form :model="formData" label-width="100px">
                <el-form-item label="分类名称">
                    <el-input v-model="formData.categoryName" placeholder="请输入分类名称" />
                </el-form-item>
                <el-form-item label="分类编码">
                    <el-input v-model="formData.categoryCode" placeholder="请输入分类编码" />
                </el-form-item>
                <el-form-item label="父级分类">
                    <el-select v-model="formData.parentId" placeholder="请选择父级分类" style="width: 100%">
                        <el-option
                            v-for="option in parentOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const dialogVisible = ref(false);
                
                const formData = reactive({
                    categoryName: '',
                    categoryCode: '',
                    parentId: 0
                });

                const parentOptions = ref([
                    { value: 0, label: '根目录' },
                    { value: 1, label: '数据采集任务目录' },
                    { value: 2, label: '　数据库采集任务' },
                    { value: 3, label: '　API接口采集任务' }
                ]);

                const showDialog = () => {
                    // 重置表单
                    Object.assign(formData, {
                        categoryName: '',
                        categoryCode: '',
                        parentId: 0
                    });
                    dialogVisible.value = true;
                };

                const handleSubmit = () => {
                    console.log('提交数据:', formData);
                    ElMessage.success('测试成功');
                    dialogVisible.value = false;
                };

                return {
                    dialogVisible,
                    formData,
                    parentOptions,
                    showDialog,
                    handleSubmit
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
