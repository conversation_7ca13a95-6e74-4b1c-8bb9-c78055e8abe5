<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="task-design-dialog"
    @close="handleClose"
  >
    <div class="task-design-container">
      <!-- 顶部工具栏 -->
      <div class="design-header">
        <div class="header-left">
          <h4>{{ dialogTitle }}</h4>
          <el-divider direction="vertical" />
          <span class="task-info" v-if="taskData.taskName">{{ taskData.taskName }}</span>
        </div>
        <div class="header-right">
          <el-button 
            v-if="mode !== 'view'"
            type="primary" 
            icon="Check"
            @click="saveTask"
            :loading="saving"
          >
            保存
          </el-button>
          <el-button
            v-if="mode === 'edit'"
            type="success"
            icon="VideoPlay"
            @click="handleExecuteTask"
            :loading="executing"
          >
            执行
          </el-button>
          <el-button 
            type="info" 
            icon="View"
            @click="previewTask"
            :loading="previewing"
          >
            预览
          </el-button>
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="design-content">
        <!-- 左侧工具面板 -->
        <div class="tools-panel">
          <div class="panel-header">
            <h4>组件工具箱</h4>
          </div>
          <div class="tool-groups">
            <!-- 数据源组件 -->
            <div class="tool-group">
              <h5>数据源</h5>
              <div class="tool-items">
                <div 
                  v-for="tool in dataSourceTools" 
                  :key="tool.type"
                  class="tool-item"
                  @click="addNode(tool.type)"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </div>

            <!-- 数据处理组件 -->
            <div class="tool-group">
              <h5>数据处理</h5>
              <div class="tool-items">
                <div 
                  v-for="tool in processTools" 
                  :key="tool.type"
                  class="tool-item"
                  @click="addNode(tool.type)"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </div>

            <!-- 控制流组件 -->
            <div class="tool-group">
              <h5>控制流</h5>
              <div class="tool-items">
                <div 
                  v-for="tool in controlTools" 
                  :key="tool.type"
                  class="tool-item"
                  @click="addNode(tool.type)"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间画布区域 -->
        <div class="canvas-panel">
          <div class="canvas-header">
            <h4>流程设计</h4>
            <div class="canvas-tools">
              <el-button size="small" icon="ZoomIn" @click="zoomIn">放大</el-button>
              <el-button size="small" icon="ZoomOut" @click="zoomOut">缩小</el-button>
              <el-button size="small" icon="Refresh" @click="resetZoom">重置</el-button>
            </div>
          </div>
          <div class="canvas-content" ref="canvasRef">
            <!-- 流程节点 -->
            <div 
              v-for="node in flowNodes" 
              :key="node.id"
              class="flow-node"
              :class="{ 'selected': selectedNodeId === node.id }"
              :style="{ left: node.x + 'px', top: node.y + 'px' }"
              @click="selectNode(node.id)"
            >
              <div class="node-header">
                <el-icon><component :is="getNodeIcon(node.type)" /></el-icon>
                <span>{{ node.name }}</span>
              </div>
              <div class="node-content">
                {{ node.description || '点击配置节点' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="properties-panel">
          <div class="panel-header">
            <h4>属性配置</h4>
          </div>
          <div class="panel-content">
            <div v-if="!selectedNode" class="no-selection">
              <el-empty description="请选择一个节点进行配置" />
            </div>
            <div v-else class="node-config">
              <el-form :model="selectedNode" label-width="80px">
                <el-form-item label="节点名称">
                  <el-input v-model="selectedNode.name" placeholder="请输入节点名称" />
                </el-form-item>
                <el-form-item label="节点描述">
                  <el-input 
                    v-model="selectedNode.description" 
                    type="textarea" 
                    :rows="2"
                    placeholder="请输入节点描述" 
                  />
                </el-form-item>
                
                <!-- 动态配置组件 -->
                <component 
                  v-if="getConfigComponent(selectedNode.type)"
                  :is="getConfigComponent(selectedNode.type)"
                  v-model="selectedNode.config"
                />
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, Database, Document, VideoPlay, CircleClose, 
  QuestionFilled, Refresh, Filter, Check, View, ZoomIn, ZoomOut
} from '@element-plus/icons-vue'

// 导入节点配置组件
import ApiCallNodeConfig from './ApiCallNodeConfig.vue'
import SqlQueryNodeConfig from './SqlQueryNodeConfig.vue'
import DataTransformNodeConfig from './DataTransformNodeConfig.vue'
import DataWriteNodeConfig from './DataWriteNodeConfig.vue'
import ConditionNodeConfig from './ConditionNodeConfig.vue'
import LoopNodeConfig from './LoopNodeConfig.vue'
import StartNodeConfig from './StartNodeConfig.vue'
import EndNodeConfig from './EndNodeConfig.vue'

// Props定义
interface Props {
  modelValue: boolean
  mode: string // add, edit, view, copy
  taskId?: number | null
  categoryId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  taskId: null,
  categoryId: null
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const saving = ref(false)
const executing = ref(false)
const previewing = ref(false)
const selectedNodeId = ref<string | null>(null)
const canvasRef = ref()

// 任务数据
const taskData = reactive({
  taskId: null as number | null,
  taskName: '',
  taskDesc: '',
  categoryId: null as number | null,
  collectionType: 'API',
  sourceType: 'API',
  status: 1
})

// 流程节点数据
const flowNodes = ref<any[]>([])

// 工具箱数据
const dataSourceTools = ref([
  { type: 'api-call', name: 'API调用', icon: 'Connection' },
  { type: 'sql-query', name: 'SQL查询', icon: 'Database' },
  { type: 'file-read', name: '文件读取', icon: 'Document' }
])

const processTools = ref([
  { type: 'data-transform', name: '数据转换', icon: 'Refresh' },
  { type: 'data-write', name: '数据写入', icon: 'Database' },
  { type: 'data-filter', name: '数据过滤', icon: 'Filter' }
])

const controlTools = ref([
  { type: 'start', name: '开始节点', icon: 'VideoPlay' },
  { type: 'end', name: '结束节点', icon: 'CircleClose' },
  { type: 'condition', name: '条件判断', icon: 'QuestionFilled' },
  { type: 'loop', name: '循环节点', icon: 'Refresh' }
])

// 计算属性
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add': return '新增数据采集任务'
    case 'edit': return '编辑数据采集任务'
    case 'view': return '查看数据采集任务'
    case 'copy': return '复制数据采集任务'
    default: return '数据采集任务设计'
  }
})

const selectedNode = computed(() => {
  return flowNodes.value.find(node => node.id === selectedNodeId.value) || null
})

// 方法
const addNode = (type: string): void => {
  const nodeId = `node_${Date.now()}`
  const newNode = {
    id: nodeId,
    type: type,
    name: getDefaultNodeName(type),
    description: '',
    x: Math.random() * 400 + 100,
    y: Math.random() * 300 + 100,
    config: getDefaultNodeConfig(type)
  }
  
  flowNodes.value.push(newNode)
  selectedNodeId.value = nodeId
}

const selectNode = (nodeId: string): void => {
  selectedNodeId.value = nodeId
}

const getDefaultNodeName = (type: string): string => {
  const nameMap: Record<string, string> = {
    'start': '开始',
    'end': '结束',
    'api-call': 'API调用',
    'sql-query': 'SQL查询',
    'data-transform': '数据转换',
    'data-write': '数据写入',
    'condition': '条件判断',
    'loop': '循环'
  }
  return nameMap[type] || '未知节点'
}

const getDefaultNodeConfig = (type: string): Record<string, any> => {
  const configMap: Record<string, Record<string, any>> = {
    'api-call': {
      url: '',
      method: 'GET',
      headers: [],
      params: [],
      authType: 'NONE'
    },
    'sql-query': {
      dataSource: '',
      sql: '',
      parameters: [],
      fetchSize: 1000
    },
    'data-transform': {
      transformRules: [],
      outputFormat: 'JSON'
    },
    'data-write': {
      targetType: 'DATABASE',
      targetConfig: {}
    }
  }
  return configMap[type] || {}
}

const getNodeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    'start': 'VideoPlay',
    'end': 'CircleClose',
    'api-call': 'Connection',
    'sql-query': 'Database',
    'data-transform': 'Refresh',
    'data-write': 'Database',
    'condition': 'QuestionFilled',
    'loop': 'Refresh'
  }
  return iconMap[type] || 'Document'
}

const getConfigComponent = (type: string): any => {
  const componentMap: Record<string, any> = {
    'api-call': ApiCallNodeConfig,
    'sql-query': SqlQueryNodeConfig,
    'data-transform': DataTransformNodeConfig,
    'data-write': DataWriteNodeConfig,
    'condition': ConditionNodeConfig,
    'loop': LoopNodeConfig,
    'start': StartNodeConfig,
    'end': EndNodeConfig
  }
  return componentMap[type] || null
}

const saveTask = async (): Promise<void> => {
  saving.value = true
  try {
    // TODO: 调用保存API
    ElMessage.success('保存成功')
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleExecuteTask = async (): Promise<void> => {
  executing.value = true
  try {
    // TODO: 调用执行API
    ElMessage.success('任务执行成功')
  } catch (error) {
    console.error('执行任务失败:', error)
    ElMessage.error('执行任务失败')
  } finally {
    executing.value = false
  }
}

const previewTask = async (): Promise<void> => {
  previewing.value = true
  try {
    // TODO: 调用预览API
    ElMessage.success('预览功能开发中')
  } catch (error) {
    console.error('预览任务失败:', error)
  } finally {
    previewing.value = false
  }
}

const zoomIn = (): void => {
  // TODO: 实现放大功能
}

const zoomOut = (): void => {
  // TODO: 实现缩小功能
}

const resetZoom = (): void => {
  // TODO: 实现重置缩放功能
}

const handleClose = (): void => {
  dialogVisible.value = false
}

// 监听props变化
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId && props.mode !== 'add') {
    // TODO: 加载任务数据
    loadTaskData(newTaskId)
  }
})

watch(() => props.categoryId, (newCategoryId) => {
  if (newCategoryId) {
    taskData.categoryId = newCategoryId
  }
})

const loadTaskData = async (taskId: number): Promise<void> => {
  try {
    // TODO: 调用API加载任务数据
    console.log('加载任务数据:', taskId)
  } catch (error) {
    console.error('加载任务数据失败:', error)
  }
}

// 初始化
onMounted(() => {
  if (props.mode === 'add') {
    // 新增模式，添加默认的开始和结束节点
    addNode('start')
    addNode('end')
  }
})
</script>

<style scoped>
.task-design-dialog :deep(.el-dialog__body) {
  padding: 0;
  height: 80vh;
}

.task-design-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.design-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h4 {
  margin: 0;
  color: #303133;
}

.task-info {
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.design-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.tools-panel {
  width: 240px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.tool-groups {
  padding: 16px;
}

.tool-group {
  margin-bottom: 24px;
}

.tool-group h5 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.tool-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.tool-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.tool-item .el-icon {
  font-size: 20px;
  color: #606266;
  margin-bottom: 4px;
}

.tool-item span {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.canvas-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.canvas-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.canvas-tools {
  display: flex;
  gap: 8px;
}

.canvas-content {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #fafafa;
  background-image:
    radial-gradient(circle, #d0d0d0 1px, transparent 1px);
  background-size: 20px 20px;
}

.flow-node {
  position: absolute;
  width: 160px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.flow-node:hover {
  border-color: #409eff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.flow-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.node-header .el-icon {
  margin-right: 6px;
  color: #606266;
}

.node-header span {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.node-content {
  padding: 8px 12px;
  font-size: 12px;
  color: #909399;
  min-height: 40px;
}

.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.node-config {
  /* 节点配置样式 */
}
</style>
