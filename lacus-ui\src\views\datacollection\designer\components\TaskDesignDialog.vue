<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="task-design-dialog"
    @close="handleClose"
  >
    <div class="task-design-container">
      <!-- 顶部工具栏 -->
      <div class="design-header">
        <div class="header-left">
          <h4>{{ dialogTitle }}</h4>
          <el-divider direction="vertical" />
          <span class="task-info" v-if="taskData.taskName">{{ taskData.taskName }}</span>
        </div>
        <div class="header-right">
          <el-button 
            v-if="mode !== 'view'"
            type="primary" 
            icon="Check"
            @click="saveTask"
            :loading="saving"
          >
            保存
          </el-button>
          <el-button
            v-if="mode === 'edit'"
            type="success"
            icon="VideoPlay"
            @click="handleExecuteTask"
            :loading="executing"
          >
            执行
          </el-button>
          <el-button 
            type="info" 
            icon="View"
            @click="previewTask"
            :loading="previewing"
          >
            预览
          </el-button>
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="design-content">
        <!-- 左侧工具面板 -->
        <div class="tools-panel">
          <div class="panel-header">
            <h4>组件工具箱</h4>
          </div>
          <div class="tool-groups">
            <!-- 数据源组件 -->
            <div class="tool-group">
              <h5>数据源</h5>
              <div class="tool-items">
                <div
                  v-for="tool in dataSourceTools"
                  :key="tool.type"
                  class="tool-item"
                  draggable="true"
                  @dragstart="handleToolDragStart($event, tool.type)"
                  @click="addNode(tool.type)"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </div>

            <!-- 数据处理组件 -->
            <div class="tool-group">
              <h5>数据处理</h5>
              <div class="tool-items">
                <div
                  v-for="tool in processTools"
                  :key="tool.type"
                  class="tool-item"
                  draggable="true"
                  @dragstart="handleToolDragStart($event, tool.type)"
                  @click="addNode(tool.type)"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </div>

            <!-- 控制流组件 -->
            <div class="tool-group">
              <h5>控制流</h5>
              <div class="tool-items">
                <div
                  v-for="tool in controlTools"
                  :key="tool.type"
                  class="tool-item"
                  draggable="true"
                  @dragstart="handleToolDragStart($event, tool.type)"
                  @click="addNode(tool.type)"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间画布区域 -->
        <div class="canvas-panel">
          <div class="canvas-header">
            <h4>流程设计</h4>
            <div class="canvas-tools">
              <el-button size="small" icon="ZoomIn" @click="zoomIn">放大</el-button>
              <el-button size="small" icon="ZoomOut" @click="zoomOut">缩小</el-button>
              <el-button size="small" icon="Refresh" @click="resetZoom">重置</el-button>
              <el-button size="small" icon="Delete" @click="deleteSelectedNode" :disabled="!selectedNodeId">删除节点</el-button>
            </div>
          </div>
          <div
            class="canvas-content"
            ref="canvasRef"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @click="handleCanvasClick"
          >
            <!-- SVG连线层 -->
            <svg class="connection-layer" :width="canvasWidth" :height="canvasHeight">
              <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7"
                        refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#409eff" />
                </marker>
              </defs>
              <!-- 连接线 -->
              <path
                v-for="connection in connections"
                :key="`${connection.from}-${connection.to}`"
                :d="getConnectionPath(connection)"
                stroke="#409eff"
                stroke-width="2"
                fill="none"
                marker-end="url(#arrowhead)"
                class="connection-line"
              />
              <!-- 临时连接线 -->
              <path
                v-if="tempConnection"
                :d="tempConnection.path"
                stroke="#409eff"
                stroke-width="2"
                stroke-dasharray="5,5"
                fill="none"
                marker-end="url(#arrowhead)"
                class="temp-connection"
              />
            </svg>

            <!-- 流程节点 -->
            <div
              v-for="node in flowNodes"
              :key="node.id"
              :data-node-id="node.id"
              class="flow-node"
              :class="{
                'selected': selectedNodeId === node.id,
                'dragging': draggingNodeId === node.id
              }"
              :style="{
                left: node.x + 'px',
                top: node.y + 'px',
                zIndex: selectedNodeId === node.id ? 1000 : 1
              }"
              @mousedown="handleNodeMouseDown($event, node.id)"
              @click.stop="selectNode(node.id)"
            >
              <!-- 输入连接点 -->
              <div
                v-if="node.type !== 'start'"
                class="connection-point input-point"
                @mousedown.stop="handleConnectionStart($event, node.id, 'input')"
              ></div>

              <!-- 节点内容 -->
              <div class="node-header">
                <el-icon><component :is="getNodeIcon(node.type)" /></el-icon>
                <span>{{ node.name }}</span>
              </div>
              <div class="node-content">
                {{ node.description || '点击配置节点' }}
              </div>

              <!-- 输出连接点 -->
              <div
                v-if="node.type !== 'end'"
                class="connection-point output-point"
                @mousedown.stop="handleConnectionStart($event, node.id, 'output')"
              ></div>
            </div>
          </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="properties-panel">
          <div class="panel-header">
            <h4>属性配置</h4>
          </div>
          <div class="panel-content">
            <div v-if="!selectedNode" class="no-selection">
              <el-empty description="请选择一个节点进行配置" />
            </div>
            <div v-else class="node-config">
              <el-form :model="selectedNode" label-width="80px">
                <el-form-item label="节点名称">
                  <el-input v-model="selectedNode.name" placeholder="请输入节点名称" />
                </el-form-item>
                <el-form-item label="节点描述">
                  <el-input 
                    v-model="selectedNode.description" 
                    type="textarea" 
                    :rows="2"
                    placeholder="请输入节点描述" 
                  />
                </el-form-item>
                
                <!-- 动态配置组件 -->
                <component 
                  v-if="getConfigComponent(selectedNode.type)"
                  :is="getConfigComponent(selectedNode.type)"
                  v-model="selectedNode.config"
                />
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Connection, Document, VideoPlay, CircleClose,
  QuestionFilled, Refresh, Filter, Check, View, ZoomIn, ZoomOut, Delete
} from '@element-plus/icons-vue'

// 导入节点配置组件
import ApiCallNodeConfig from './ApiCallNodeConfig.vue'
import SqlQueryNodeConfig from './SqlQueryNodeConfig.vue'
import DataTransformNodeConfig from './DataTransformNodeConfig.vue'
import DataWriteNodeConfig from './DataWriteNodeConfig.vue'
import ConditionNodeConfig from './ConditionNodeConfig.vue'
import LoopNodeConfig from './LoopNodeConfig.vue'
import StartNodeConfig from './StartNodeConfig.vue'
import EndNodeConfig from './EndNodeConfig.vue'

// Props定义
interface Props {
  modelValue: boolean
  mode: string // add, edit, view, copy
  taskId?: number | null
  categoryId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  taskId: null,
  categoryId: null
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const saving = ref(false)
const executing = ref(false)
const previewing = ref(false)
const selectedNodeId = ref<string | null>(null)
const canvasRef = ref()

// 画布相关
const canvasWidth = ref(1200)
const canvasHeight = ref(800)
const draggingNodeId = ref<string | null>(null)
const dragOffset = ref({ x: 0, y: 0 })
const isConnecting = ref(false)
const connectionStart = ref<{ nodeId: string, type: 'input' | 'output' } | null>(null)
const tempConnection = ref<{ path: string } | null>(null)

// 连接线数据
const connections = ref<Array<{ from: string, to: string }>>([])

// 鼠标位置
const mousePosition = ref({ x: 0, y: 0 })

// 任务数据
const taskData = reactive({
  taskId: null as number | null,
  taskName: '',
  taskDesc: '',
  categoryId: null as number | null,
  collectionType: 'API',
  sourceType: 'API',
  status: 1
})

// 流程节点数据
const flowNodes = ref<any[]>([])

// 工具箱数据
const dataSourceTools = ref([
  { type: 'api-call', name: 'API调用', icon: Connection },
  { type: 'sql-query', name: 'SQL查询', icon: Document },
  { type: 'file-read', name: '文件读取', icon: Document }
])

const processTools = ref([
  { type: 'data-transform', name: '数据转换', icon: Refresh },
  { type: 'data-write', name: '数据写入', icon: Document },
  { type: 'data-filter', name: '数据过滤', icon: Filter }
])

const controlTools = ref([
  { type: 'start', name: '开始节点', icon: VideoPlay },
  { type: 'end', name: '结束节点', icon: CircleClose },
  { type: 'condition', name: '条件判断', icon: QuestionFilled },
  { type: 'loop', name: '循环节点', icon: Refresh }
])

// 计算属性
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add': return '新增数据采集任务'
    case 'edit': return '编辑数据采集任务'
    case 'view': return '查看数据采集任务'
    case 'copy': return '复制数据采集任务'
    default: return '数据采集任务设计'
  }
})

const selectedNode = computed(() => {
  return flowNodes.value.find(node => node.id === selectedNodeId.value) || null
})

// 方法
const addNode = (type: string): void => {
  const nodeId = `node_${Date.now()}`
  const newNode = {
    id: nodeId,
    type: type,
    name: getDefaultNodeName(type),
    description: '',
    x: Math.random() * 400 + 100,
    y: Math.random() * 300 + 100,
    config: getDefaultNodeConfig(type)
  }

  flowNodes.value.push(newNode)
  selectedNodeId.value = nodeId
}

const handleToolDragStart = (event: DragEvent, toolType: string): void => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', toolType)
    event.dataTransfer.effectAllowed = 'copy'
  }
}

const selectNode = (nodeId: string): void => {
  selectedNodeId.value = nodeId
}

const getDefaultNodeName = (type: string): string => {
  const nameMap: Record<string, string> = {
    'start': '开始',
    'end': '结束',
    'api-call': 'API调用',
    'sql-query': 'SQL查询',
    'data-transform': '数据转换',
    'data-write': '数据写入',
    'condition': '条件判断',
    'loop': '循环'
  }
  return nameMap[type] || '未知节点'
}

const getDefaultNodeConfig = (type: string): Record<string, any> => {
  const configMap: Record<string, Record<string, any>> = {
    'api-call': {
      url: '',
      method: 'GET',
      headers: [],
      params: [],
      authType: 'NONE'
    },
    'sql-query': {
      dataSource: '',
      sql: '',
      parameters: [],
      fetchSize: 1000
    },
    'data-transform': {
      transformRules: [],
      outputFormat: 'JSON'
    },
    'data-write': {
      targetType: 'DATABASE',
      targetConfig: {}
    }
  }
  return configMap[type] || {}
}

const getNodeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    'start': 'VideoPlay',
    'end': 'CircleClose',
    'api-call': 'Connection',
    'sql-query': 'Database',
    'data-transform': 'Refresh',
    'data-write': 'Database',
    'condition': 'QuestionFilled',
    'loop': 'Refresh'
  }
  return iconMap[type] || 'Document'
}

const getConfigComponent = (type: string): any => {
  const componentMap: Record<string, any> = {
    'api-call': ApiCallNodeConfig,
    'sql-query': SqlQueryNodeConfig,
    'data-transform': DataTransformNodeConfig,
    'data-write': DataWriteNodeConfig,
    'condition': ConditionNodeConfig,
    'loop': LoopNodeConfig,
    'start': StartNodeConfig,
    'end': EndNodeConfig
  }
  return componentMap[type] || null
}

const saveTask = async (): Promise<void> => {
  saving.value = true
  try {
    // TODO: 调用保存API
    ElMessage.success('保存成功')
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleExecuteTask = async (): Promise<void> => {
  executing.value = true
  try {
    // TODO: 调用执行API
    ElMessage.success('任务执行成功')
  } catch (error) {
    console.error('执行任务失败:', error)
    ElMessage.error('执行任务失败')
  } finally {
    executing.value = false
  }
}

const previewTask = async (): Promise<void> => {
  previewing.value = true
  try {
    // TODO: 调用预览API
    ElMessage.success('预览功能开发中')
  } catch (error) {
    console.error('预览任务失败:', error)
  } finally {
    previewing.value = false
  }
}

const zoomIn = (): void => {
  // TODO: 实现放大功能
}

const zoomOut = (): void => {
  // TODO: 实现缩小功能
}

const resetZoom = (): void => {
  // TODO: 实现重置缩放功能
}

const handleClose = (): void => {
  dialogVisible.value = false
}

// 画布交互函数
const handleDrop = (event: DragEvent): void => {
  event.preventDefault()
  const nodeType = event.dataTransfer?.getData('text/plain')
  if (!nodeType) return

  const rect = canvasRef.value?.getBoundingClientRect()
  if (!rect) return

  const x = event.clientX - rect.left - 80 // 节点宽度的一半
  const y = event.clientY - rect.top - 40  // 节点高度的一半

  addNodeAtPosition(nodeType, x, y)
}

const handleDragOver = (event: DragEvent): void => {
  event.preventDefault()
}

const handleCanvasClick = (event: MouseEvent): void => {
  // 点击空白区域取消选择
  if (event.target === canvasRef.value) {
    selectedNodeId.value = null
  }
}

const addNodeAtPosition = (type: string, x: number, y: number): void => {
  const nodeId = `node_${Date.now()}`
  const newNode = {
    id: nodeId,
    type: type,
    name: getDefaultNodeName(type),
    description: '',
    x: Math.max(0, x),
    y: Math.max(0, y),
    config: getDefaultNodeConfig(type)
  }

  flowNodes.value.push(newNode)
  selectedNodeId.value = nodeId
}

const handleNodeMouseDown = (event: MouseEvent, nodeId: string): void => {
  event.preventDefault()
  draggingNodeId.value = nodeId

  const node = flowNodes.value.find(n => n.id === nodeId)
  if (!node) return

  const startX = event.clientX
  const startY = event.clientY
  const startNodeX = node.x
  const startNodeY = node.y

  const handleMouseMove = (e: MouseEvent): void => {
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY

    node.x = Math.max(0, startNodeX + deltaX)
    node.y = Math.max(0, startNodeY + deltaY)
  }

  const handleMouseUp = (): void => {
    draggingNodeId.value = null
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleConnectionStart = (event: MouseEvent, nodeId: string, type: 'input' | 'output'): void => {
  event.preventDefault()
  event.stopPropagation()

  connectionStart.value = { nodeId, type }
  isConnecting.value = true

  const handleMouseMove = (e: MouseEvent): void => {
    if (!connectionStart.value) return

    const rect = canvasRef.value?.getBoundingClientRect()
    if (!rect) return

    const startNode = flowNodes.value.find(n => n.id === connectionStart.value!.nodeId)
    if (!startNode) return

    const startX = startNode.x + 80 // 节点中心
    const startY = startNode.y + 40
    const endX = e.clientX - rect.left
    const endY = e.clientY - rect.top

    tempConnection.value = {
      path: `M ${startX} ${startY} L ${endX} ${endY}`
    }
  }

  const handleMouseUp = (e: MouseEvent): void => {
    if (connectionStart.value) {
      // 检查是否连接到了另一个节点
      const target = e.target as HTMLElement
      const targetNode = target.closest('.flow-node')
      if (targetNode) {
        const targetNodeId = targetNode.getAttribute('data-node-id')
        if (targetNodeId && targetNodeId !== connectionStart.value.nodeId) {
          // 创建连接
          if (connectionStart.value.type === 'output') {
            connections.value.push({
              from: connectionStart.value.nodeId,
              to: targetNodeId
            })
          }
        }
      }
    }

    // 清理
    connectionStart.value = null
    isConnecting.value = false
    tempConnection.value = null
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const getConnectionPath = (connection: { from: string, to: string }): string => {
  const fromNode = flowNodes.value.find(n => n.id === connection.from)
  const toNode = flowNodes.value.find(n => n.id === connection.to)

  if (!fromNode || !toNode) return ''

  const startX = fromNode.x + 160 // 输出点位置
  const startY = fromNode.y + 40
  const endX = toNode.x // 输入点位置
  const endY = toNode.y + 40

  // 创建贝塞尔曲线
  const controlX1 = startX + 50
  const controlY1 = startY
  const controlX2 = endX - 50
  const controlY2 = endY

  return `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`
}

const deleteSelectedNode = (): void => {
  if (!selectedNodeId.value) return

  // 删除节点
  const nodeIndex = flowNodes.value.findIndex(n => n.id === selectedNodeId.value)
  if (nodeIndex > -1) {
    flowNodes.value.splice(nodeIndex, 1)
  }

  // 删除相关连接
  connections.value = connections.value.filter(
    conn => conn.from !== selectedNodeId.value && conn.to !== selectedNodeId.value
  )

  selectedNodeId.value = null
}

// 监听props变化
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId && props.mode !== 'add') {
    // TODO: 加载任务数据
    loadTaskData(newTaskId)
  }
})

watch(() => props.categoryId, (newCategoryId) => {
  if (newCategoryId) {
    taskData.categoryId = newCategoryId
  }
})

const loadTaskData = async (taskId: number): Promise<void> => {
  try {
    // TODO: 调用API加载任务数据
    console.log('加载任务数据:', taskId)
  } catch (error) {
    console.error('加载任务数据失败:', error)
  }
}

// 初始化
onMounted(() => {
  if (props.mode === 'add') {
    // 新增模式，添加默认的开始和结束节点
    addNode('start')
    addNode('end')
  }
})
</script>

<style scoped>
.task-design-dialog :deep(.el-dialog__body) {
  padding: 0;
  height: 80vh;
}

.task-design-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.design-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h4 {
  margin: 0;
  color: #303133;
}

.task-info {
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.design-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.tools-panel {
  width: 240px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.tool-groups {
  padding: 16px;
}

.tool-group {
  margin-bottom: 24px;
}

.tool-group h5 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.tool-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.tool-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.tool-item .el-icon {
  font-size: 20px;
  color: #606266;
  margin-bottom: 4px;
}

.tool-item span {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.canvas-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.canvas-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.canvas-tools {
  display: flex;
  gap: 8px;
}

.canvas-content {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #fafafa;
  background-image:
    radial-gradient(circle, #d0d0d0 1px, transparent 1px);
  background-size: 20px 20px;
}

.flow-node {
  position: absolute;
  width: 160px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.flow-node:hover {
  border-color: #409eff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.flow-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.node-header .el-icon {
  margin-right: 6px;
  color: #606266;
}

.node-header span {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.node-content {
  padding: 8px 12px;
  font-size: 12px;
  color: #909399;
  min-height: 40px;
}

.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 连接点样式 */
.connection-point {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #409eff;
  border-radius: 50%;
  background: white;
  cursor: crosshair;
  z-index: 10;
}

.input-point {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.output-point {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point:hover {
  background: #409eff;
  border-color: #66b1ff;
}

/* SVG连线层 */
.connection-layer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  cursor: pointer;
  pointer-events: stroke;
}

.connection-line:hover {
  stroke: #66b1ff;
  stroke-width: 3;
}

.temp-connection {
  opacity: 0.7;
}

/* 节点拖拽状态 */
.flow-node.dragging {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}
</style>
